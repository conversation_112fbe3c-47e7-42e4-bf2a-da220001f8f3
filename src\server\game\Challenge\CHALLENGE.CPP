/*
 * Copyright (C) 2008-2014 TrinityCore <http://www.trinitycore.org/>
 *
 * This program is free software; you can redistribute it and/or modify it
 * under the terms of the GNU General Public License as published by the
 * Free Software Foundation; either version 2 of the License, or (at your
 * option) any later version.
 *
 * This program is distributed in the hope that it will be useful, but WITHOUT
 * ANY WARRANTY; without even the implied warranty of MERCHANTABILITY or
 * FITNESS FOR A PARTICULAR PURPOSE. See the GNU General Public License for
 * more details.
 *
 * You should have received a copy of the GNU General Public License along
 * with this program. If not, see <http://www.gnu.org/licenses/>.
 */

#include "Challenge.h"
#include "ChallengeMgr.h"
#include "ChallengeModePackets.h"
#include "Chat.h"
#include "GroupMgr.h"
#include "DatabaseEnv.h"
#include "InstancePackets.h"
#include "Map.h"
#include "MiscPackets.h"
#include "WorldStatePackets.h"

Challenge::Challenge(Map* map, Player* player, uint32 instanceID, Scenario* scenario) : InstanceScript(map), _instanceScript(nullptr), _challengeEntry(nullptr), _isKeyDepleted(false), _scenario(scenario)
{
    if (!player)
    {
        _canRun = false;
        return;
    }

    _checkStart = true;
    _canRun = true;
    _creator = player->GetGUID();
    _instanceID = instanceID;
    _challengeTimer = 0;
    _affixQuakingTimer = 0;
    _deathCount = 0;
    _complete = false;
    _run = false;
    _item = nullptr;

    ASSERT(map);
    _map = map;
    _mapID = _map->GetId();

    if (Group* group = player->GetGroup())
    {
        group->m_challengeInstanceID = _instanceID;
        m_ownerGuid = group->m_challengeOwner;
        m_itemGuid = group->m_challengeItem;
        _challengeEntry = group->m_challengeEntry;
        m_gguid = group->GetGUID();

        if (!m_itemGuid)
        {
            _canRun = false;
            ChatHandler(player).PSendSysMessage("Error: Key not found.");
            return;
        }

        for (GroupReference* itr = group->GetFirstMember(); itr != nullptr; itr = itr->next())
            if (Player* member = itr->getSource())
                _challengers.insert(member->GetGUID());

        _affixes = group->m_affixes;
        _challengeLevel = group->m_challengeLevel;
    }
    else
    {
        m_ownerGuid = player->GetGUID();
        if (Item* item = player->GetItemByEntry(138019))
            m_itemGuid = item->GetGUID();
        else
        {
            _canRun = false;
            ChatHandler(player).PSendSysMessage("Error: Key not found.");
            return;
        }
        _challengeLevel = player->m_challengeKeyInfo.Level;
        _challengeEntry = player->m_challengeKeyInfo.challengeEntry;

        _affixes.fill(0);   //词缀在这里判断？大秘境等级超过3级显示一个 超过6级显示2个 超过9级显示三个
        if (_challengeLevel > 3)
            _affixes[0] = player->m_challengeKeyInfo.Affix;
        if (_challengeLevel > 6)
            _affixes[1] = player->m_challengeKeyInfo.Affix1;
        if (_challengeLevel > 9)
            _affixes[2] = player->m_challengeKeyInfo.Affix2;
    }

    if (!_challengeEntry)
    {
        ChatHandler(player).PSendSysMessage("Error: Is not a challenge map.");
        _canRun = false;
        return;
    }
    map->m_ChallengeLv = _challengeLevel;
    _challengers.insert(_creator);

    _rewardLevel = CHALLENGE_NOT_IN_TIMER;

    for (auto const& affix : _affixes)
        _affixesTest.set(affix);    //将词缀存进容器，需要时用HasAffix对比判断给予相应操作，affix值对应KeystoneAffix.db2

    for (uint8 i = 0; i < CHALLENGE_TIMER_LEVEL_3; i++)
        _chestTimers[i] = _challengeEntry->CriteriaCount[i];        //副本/宝箱倒计时 例如魔法回廊 MapChallengeMode.db2的CriteriaCount[0] = 2700 （45分钟）??不会发给客户端
}

Challenge::~Challenge()
{
    if (InstanceScript* script = GetInstanceScript())
        script->SetChallenge(nullptr);

    if (_map)
        _map->m_ChallengeLv = 0;

    _instanceScript = nullptr;
}

// 玩家进入挑战副本脚本事件处理函数 - 当玩家进入大秘境副本时自动调用
// 功能作用：为进入挑战副本的玩家施放挑战者负担光环，激活挑战模式的玩家词缀效果
// 设计原理：基于InstanceScript事件系统，确保所有进入挑战副本的玩家都获得必要的挑战状态
// 技术优势：自动化状态管理，无需手动干预，保证挑战模式的一致性和完整性
// 语义含义：代表玩家正式进入挑战状态，承担挑战模式带来的额外负担和限制
// 底层机制：基于InstanceScript事件系统，通过Map::AddPlayerToMap触发调用链
// 系统关联：与InstanceScript::OnPlayerEnterForScript配合，实现玩家进入事件的统一处理
// 调用时机：玩家传送进入挑战副本时、副本重置后重新进入时、断线重连进入时
// 参数说明：
//   player: 进入副本的玩家对象指针，用于施放挑战者负担光环和状态管理
void Challenge::OnPlayerEnterForScript(Player* player)
{
    // 安全检查：确保玩家对象有效 - 防止空指针访问导致的服务器崩溃
    if (!player)
        return;

    // 为玩家施放挑战者负担光环 - 激活玩家相关的挑战词缀效果
    // ChallengersBurden (206151): 包含溢出、胆怯、重伤等玩家词缀的复合光环
    // 具体效果包括：
    //   EFFECT_1: 溢出词缀 - 过量治疗转化为吸收护盾
    //   EFFECT_2: 胆怯词缀 - 降低坦克专精的仇恨生成效率
    //   EFFECT_3: 重伤词缀 - 生命值低于90%时周期性应用重伤创口
    // 参数true表示触发模式施法，跳过法力消耗、冷却时间和施法时间检查
    player->CastSpell(player, ChallengersBurden, true);
}

// 玩家离开挑战副本脚本事件处理函数 - 当玩家离开大秘境副本时自动调用
// 功能作用：移除玩家身上的挑战者负担光环，清理挑战模式相关的状态效果
// 设计原理：确保玩家离开挑战环境后不会保留挑战状态，避免影响正常游戏体验
// 技术优势：自动化状态清理，防止挑战效果泄露到非挑战环境中
// 语义含义：代表玩家脱离挑战状态，恢复正常的游戏状态和能力
// 底层机制：基于InstanceScript事件系统，通过光环移除实现状态清理
// 系统关联：与InstanceScript::OnPlayerLeaveForScript配合，实现玩家离开事件的统一处理
// 调用时机：玩家传送离开挑战副本时、副本重置时、玩家断线时、挑战完成时
// 参数说明：
//   player: 离开副本的玩家对象指针，用于移除挑战者负担光环
void Challenge::OnPlayerLeaveForScript(Player* player)
{
    // 安全检查：确保玩家对象有效 - 防止空指针访问导致的服务器崩溃
    if (!player)
        return;

    // 移除玩家身上的挑战者负担光环 - 清理所有挑战模式相关的玩家效果
    // 包括溢出词缀、胆怯词缀、重伤词缀等所有玩家相关的挑战状态
    // 确保玩家离开挑战副本后不会保留任何挑战模式的负面效果
    player->RemoveAura(ChallengersBurden);
}

void Challenge::OnPlayerDiesForScript(Player* /*player*/)
{
    ++_deathCount;
    ModChallengeTimer(Seconds(5).count());

    SendStartElapsedTimer();

    WorldPackets::ChallengeMode::ChallengeModeUpdateDeathCount packet;
    packet.DeathCount = _deathCount;
    BroadcastPacket(packet.Write());
}

void Challenge::OnCreatureCreateForScript(Creature* creature)
{
    if (!creature || creature->isTrigger() || creature->IsControlledByPlayer() || creature->GetCreatureType() == CREATURE_TYPE_CRITTER)
        return;

    Unit* owner = creature->GetAnyOwner();
    if (owner && owner->IsPlayer())
        return;

    creature->AddAura(ChallengersMight, creature);
}

void Challenge::OnCreatureUpdateDifficulty(Creature* creature)
{
    if (!creature || creature->isTrigger() || creature->IsControlledByPlayer() || creature->GetCreatureType() == CREATURE_TYPE_CRITTER)
        return;

    Unit* owner = creature->GetAnyOwner();
    if (owner && owner->IsPlayer())
        return;

    creature->AddAura(ChallengersMight, creature);
}

void Challenge::OnCreatureRemoveForScript(Creature* /*creature*/) { }

void Challenge::EnterCombatForScript(Creature* creature, Unit* /*enemy*/)
{
    if (!creature || creature->isTrigger() || creature->IsControlledByPlayer())
        return;

    Unit* owner = creature->GetAnyOwner();
    if (owner && owner->IsPlayer())
        return;

    if (!creature->HasAura(ChallengersMight))
        creature->AddAura(ChallengersMight, creature);
}

void Challenge::CreatureDiesForScript(Creature* creature, Unit* /*killer*/)
{
    if (!creature || creature->isTrigger() || creature->IsControlledByPlayer() || !creature->IsHostileToPlayers() || creature->GetCreatureType() == CREATURE_TYPE_CRITTER)
        return;

    if (creature->GetAnyOwner() || creature->IsOnVehicle())
        return;

    if (HasAffix(Affixes::Bolstering) && !creature->IsAffixDisabled(Affixes::Bolstering))
        creature->CastSpell(creature, ChallengerBolstering, true);

    if (HasAffix(Affixes::Sanguine) && !creature->IsAffixDisabled(Affixes::Sanguine))
        creature->CastSpell(creature, ChallengerSanguine, true);

    if (HasAffix(Affixes::Bursting) && !creature->IsAffixDisabled(Affixes::Bursting))
        creature->CastSpell(creature, ChallengerBursting, true);
}

void Challenge::OnUnitCharmed(Unit* unit, Unit* /*charmer*/)
{
    if (!unit || !unit->ToCreature())
        return;

    unit->RemoveAura(ChallengerBolstering);
    unit->RemoveAura(ChallengerRaging);
    unit->RemoveAura(ChallengersMight);
    unit->RemoveAura(207850); // Bond of Strength
}

void Challenge::OnUnitRemoveCharmed(Unit* unit, Unit* /*charmer*/)
{
    if (!unit || !unit->ToCreature())
        return;

    unit->AddAura(ChallengersMight, unit);
    // unit->RemoveAura(ChallengerBolstering);
}

// 挑战模式核心更新函数 - 大秘境系统的主要逻辑处理中心，每个服务器Tick都会调用
// 功能作用：管理挑战模式的完整生命周期，包括状态检查、计时器更新、词缀处理、钥石耗尽机制
// 设计原理：基于差分时间的增量更新模式，通过状态机管理挑战的不同阶段（准备、进行、完成）
// 技术优势：高效的时间管理、模块化的功能处理、自动化的状态转换、实时的词缀效果应用
// 语义含义：大秘境挑战的心跳函数，维持挑战系统的持续运行和状态同步
// 底层机制：基于InstanceScript的Update机制，通过FunctionProcessor处理延迟执行的任务
// 系统关联：与Map、Group、Player、Database等多个系统交互，实现完整的挑战体验
// 核心职责：
//   1. 延迟函数处理 - 管理定时任务和异步操作
//   2. 挑战状态管理 - 控制挑战的开始、进行、完成状态
//   3. 计时器更新 - 维护挑战总时间和词缀特定计时器
//   4. 词缀效果处理 - 应用震荡等周期性词缀效果
//   5. 钥石耗尽检查 - 处理超时后的钥石降级和状态重置
// 参数说明：
//   diff: 自上次更新以来的时间差（毫秒），用于增量时间计算和状态更新
void Challenge::Update(uint32 diff)
{
    // 更新延迟函数处理器 - 处理所有待执行的定时任务和异步操作
    // FunctionProcessor管理诸如挑战开始倒计时、门墙移除等延迟执行的功能
    // 这是挑战系统中异步任务调度的核心机制，确保时序相关操作的正确执行
    m_Functions.Update(diff);

    // 挑战完成状态检查 - 如果挑战已完成则停止所有更新处理
    if (_complete) // Stop update if complete
        return;

    // 挑战开始前的准备阶段处理 - 检查是否满足开始条件并启动挑战
    // 只有在挑战未运行且需要检查开始条件时才执行此逻辑
    if (!_run && _checkStart)
    {
        // 检查挑战开始条件 - 验证所有挑战者是否已进入副本
        // CanStart()检查当前副本玩家数量是否等于挑战者集合大小
        if (CanStart())
        {
            // 启动挑战 - 执行挑战开始的完整流程
            // 包括玩家传送、计时器重置、数据包发送、状态更新等
            Start();
            // 召唤挑战门墙 - 在挑战开始时激活副本入口的阻挡门
            // 防止挑战进行中有额外玩家进入副本，维护挑战的公平性
            SummonWall(nullptr);
        }
        // 准备阶段结束，等待下次Update检查或直接返回
        return;
    }

    // 挑战计时器更新 - 累加挑战进行的总时间（毫秒）
    // 这是大秘境系统的核心计时机制，用于奖励等级判定和超时检查
    _challengeTimer += diff;

    // 震荡词缀计时器处理 - 管理震荡词缀的周期性触发机制
    // 震荡词缀每20秒对所有玩家施放一次ChallengerQuake光环
    if (_affixQuakingTimer)
    {
        // 检查震荡词缀计时器是否到期 - 判断是否需要触发震荡效果
        if (_affixQuakingTimer <= diff)
        {
            // 对副本内所有玩家应用震荡词缀效果 - 使用Lambda表达式遍历所有玩家
            _map->ApplyOnEveryPlayer([&](Player* plr)
            {
                // 震荡词缀应用条件检查 - 确保玩家满足接受震荡效果的所有条件
                // CanContact(): 玩家在线且可交互（非离线、非传送中）
                // !HasAura(ChallengerQuake): 玩家当前没有震荡光环（避免重复施放）
                // !HasFlag(UNIT_FLAG2_NO_ACTIONS): 玩家不处于无法行动状态（如昏迷、石化）
                // !IsOnVehicle(): 玩家不在载具上（载具状态下不受震荡影响）
                if (plr->CanContact() && !plr->HasAura(ChallengerQuake) && !plr->HasFlag(UNIT_FIELD_FLAGS_2, UNIT_FLAG2_NO_ACTIONS) && !plr->IsOnVehicle())
                    // 为符合条件的玩家添加震荡光环 - ChallengerQuake (240447) 震荡词缀法术
                    // 震荡效果会在一定时间后引发范围伤害，要求玩家分散站位
                    plr->AddAura(ChallengerQuake, plr);
            });
            // 重置震荡词缀计时器 - 设置下次震荡触发的间隔时间（20秒 = 20000毫秒）
            _affixQuakingTimer = 20000;
        }
        else
            // 递减震荡词缀计时器 - 减去本次Update的时间差，逐步接近触发时间
            _affixQuakingTimer -= diff;
    }

    // 钥石耗尽检查机制 - 处理挑战超时后的钥石降级和状态重置
    // 当挑战时间超过副本时间限制且钥石尚未被标记为耗尽时触发
    if (!_isKeyDepleted && GetChallengeTimer() > _challengeEntry->CriteriaCount[0])
    {
        // 标记钥石为耗尽状态 - 防止重复执行耗尽逻辑
        _isKeyDepleted = true;

        // 重置队伍的挑战相关状态 - 清理队伍的大秘境配置信息
        if (Group* group = sGroupMgr->GetGroupByGUID(m_gguid))
        {
            // 清空队伍的挑战模式条目引用 - 断开与当前挑战的关联
            group->m_challengeEntry = nullptr;
            // 重置队伍的挑战等级 - 清除大秘境等级信息
            group->m_challengeLevel = 0;
            // 清空队伍的词缀配置 - 移除所有激活的挑战词缀
            group->m_affixes.fill(0);
            // 重置队伍副本难度为普通史诗难度 - 从大秘境模式降级为普通史诗模式
            group->SetDungeonDifficultyID(DIFFICULTY_MYTHIC_DUNGEON);
            // 清空队伍的挑战实例ID - 断开与当前挑战实例的关联
            group->m_challengeInstanceID = 0;
        }

        // 钥石物品处理和降级逻辑 - 处理超时后的钥石状态更新
        _item = nullptr;
        // 查找钥石拥有者 - 在当前地图中搜索钥石的原始拥有者
        Player* keyOwner = ObjectAccessor::FindPlayer(_map, m_ownerGuid);
        if (keyOwner)
            // 获取钥石物品引用 - 通过物品GUID获取实际的钥石物品对象
            _item = keyOwner->GetItemByGuid(m_itemGuid);

        // 执行钥石降级处理 - 根据钥石物品的可用性选择不同的处理方式
        if (_item)
            // 钥石物品存在时的降级处理 - 调用玩家的钥石降级函数
            // ChallengeKeyCharded会将钥石等级降低，并更新钥石的挑战ID和状态
            keyOwner->ChallengeKeyCharded(_item, _challengeLevel);
        else
        {
            // 钥石物品不存在时的备用处理逻辑 - 尝试重新查找钥石拥有者和物品
            if (keyOwner = ObjectAccessor::FindPlayer(m_ownerGuid))
            {
                // 再次尝试获取钥石物品 - 处理可能的延迟或缓存问题
                if (_item = keyOwner->GetItemByGuid(m_itemGuid))
                    // 找到钥石后执行降级处理
                    keyOwner->ChallengeKeyCharded(_item, _challengeLevel);
                else
                    // 钥石物品确实不存在时的数据库直接更新 - 清理数据库中的钥石状态
                    // 设置KeyIsCharded=0（钥石未耗尽）和InstanceID=0（无关联实例）
                    CharacterDatabase.PQuery("UPDATE challenge_key SET KeyIsCharded = 0, InstanceID = 0 WHERE guid = %u", m_ownerGuid.GetGUIDLow());
            }
            else
                // 钥石拥有者完全不存在时的数据库更新 - 处理玩家离线或删除角色的情况
                // 确保数据库中的钥石状态得到正确清理，避免数据不一致
                CharacterDatabase.PQuery("UPDATE challenge_key SET KeyIsCharded = 0, InstanceID = 0 WHERE guid = %u", m_ownerGuid.GetGUIDLow());
        }
    }
}

bool Challenge::CanStart()
{
    if (_run)
        return true;

    return _map->GetPlayerCount() == _challengers.size(); // wait when group complete
}
//开始大秘境
void Challenge::Start()
{
    if (!_canRun)
        return;

    Player* keyOwner = ObjectAccessor::FindPlayer(m_ownerGuid);
    if (!keyOwner)
        return;

    _item = keyOwner->GetItemByGuid(m_itemGuid);
    if (!_item)
        return;

    keyOwner->m_challengeKeyInfo.InstanceID = keyOwner->GetInstanceId();
    keyOwner->m_challengeKeyInfo.needUpdate = true;

    _isKeyDepleted = false;

    float x = 0.0f;
    float y = 0.0f;
    float z = 0.0f;
    float o = 0.0f;

    if(!sChallengeMgr->GetStartPosition(_map->GetId(), x, y, z, o, m_ownerGuid))
        return;

    _map->ApplyOnEveryPlayer([&](Player* player)
    {
        if (player->CanContact())
        {
            player->SafeTeleport(_map->GetId(), x, y, z, o);
            _scenario->SendStepUpdate(player, true);
        }
    });

    BroadcastPacket(WorldPackets::ChallengeMode::ChallengeModeReset(_mapID).Write());

    SendStartTimer();
    SendChallengeModeStart();

    m_Functions.AddFunction([this]() -> void
    {
        if (!this)
            return;

        _challengeTimer = 0;
        SendStartElapsedTimer();

        for (ObjectGuid guid : GetInstanceScript()->_challengeDoorGuids)
            if (GameObject* challengeDoor = sObjectAccessor->FindGameObject(guid))
                challengeDoor->Delete();

        _run = true;

    }, m_Functions.CalculateTime(10 * IN_MILLISECONDS));

    _checkStart = false;

    if (HasAffix(Affixes::Quaking))
        _affixQuakingTimer = 20000;
}
//完成大秘境
void Challenge::Complete()
{
    if (_complete)
        return;

    _complete = true;

    Player* keyOwner = ObjectAccessor::FindPlayer(_map, m_ownerGuid);
    _item = nullptr;
    if (keyOwner)
        _item = keyOwner->GetItemByGuid(m_itemGuid);

    HitTimer(); //暂停计时器然后刷新箱子

    WorldPackets::Misc::StopElapsedTimer stopElapsedTimer;
    stopElapsedTimer.TimerID = WORLD_TIMER_TYPE_CHALLENGE_MODE;
    BroadcastPacket(stopElapsedTimer.Write());

    if (_challengeEntry)
    {
        WorldPackets::ChallengeMode::ChallengeModeComplete complete;
        complete.MapID = _mapID;
        complete.CompletionMilliseconds = _challengeTimer;
        complete.StartedChallengeLevel = _challengeLevel;
        complete.ChallengeID = _challengeEntry->ID;
        BroadcastPacket(complete.Write());
    }

    if (Group* group = sGroupMgr->GetGroupByGUID(m_gguid))
    {
        group->m_challengeEntry = nullptr;
        group->m_challengeLevel = 0;
        group->m_affixes.fill(0);
        group->SetDungeonDifficultyID(DIFFICULTY_MYTHIC_DUNGEON);
        group->m_challengeInstanceID = 0;
    }

    /// Reward part
    if (_item)
    {
        if (!_isKeyDepleted)
        {
            _item->SetModifier(ITEM_MODIFIER_CHALLENGE_ID, *Trinity::Containers::SelectRandomWeightedContainerElement(sDB2Manager.GetChallngeMaps(), sDB2Manager.GetChallngesWeight()));
            _item->SetModifier(ITEM_MODIFIER_CHALLENGE_KEYSTONE_LEVEL, std::min(_challengeLevel + _rewardLevel, sWorld->getIntConfig(CONFIG_CHALLENGE_LEVEL_LIMIT)));
        }
        else
            keyOwner->ChallengeKeyCharded(_item, _challengeLevel);

        keyOwner->UpdateChallengeKey(_item);
        _item->SetState(ITEM_CHANGED, keyOwner);
    }
    else
    {
        if (keyOwner = ObjectAccessor::FindPlayer(m_ownerGuid))
        {
            if (_item = keyOwner->GetItemByGuid(m_itemGuid))
                keyOwner->ChallengeKeyCharded(_item, _challengeLevel);
            else
                CharacterDatabase.PQuery("UPDATE challenge_key SET KeyIsCharded = 0, InstanceID = 0 WHERE guid = %u", m_ownerGuid.GetGUIDLow());
        }
        else
            CharacterDatabase.PQuery("UPDATE challenge_key SET KeyIsCharded = 0, InstanceID = 0 WHERE guid = %u", m_ownerGuid.GetGUIDLow());
    }

    auto challengeData = new ChallengeData;
    challengeData->ID = sObjectMgr->GetGenerator<HighGuid::Scenario>()->Generate();
    challengeData->MapID = _mapID;
    challengeData->RecordTime = _challengeTimer;
    challengeData->Date = time(nullptr);
    challengeData->ChallengeLevel = _challengeLevel;
    challengeData->TimerLevel = _rewardLevel;
    challengeData->ChallengeID = _challengeEntry ? _challengeEntry->ID : 0;
    challengeData->Affixes = _affixes;
    challengeData->GuildID = 0;
    if (InstanceScript* script = GetInstanceScript())
        challengeData->ChestID = script->_challengeChest.GetEntry();
    else
        challengeData->ChestID = 0;

    std::map<ObjectGuid::LowType /*guild*/, uint32> guildCounter;
    std::map<uint32, std::string> anticheatData;
    _map->ApplyOnEveryPlayer([&](Player* player)
    {
        ChallengeMember member;
        member.guid = player->GetGUID();
        member.specId = player->GetUInt32Value(PLAYER_FIELD_CURRENT_SPEC_ID);
        member.Date = time(nullptr);
        member.ChallengeLevel = _challengeLevel;
        if (InstanceScript* script = GetInstanceScript())
            member.ChestID = script->_challengeChest.GetEntry();
        else
            member.ChestID = 0;

        anticheatData[player->GetGUIDLow()] = player->GetName();

        if (player->GetGuildId())
            guildCounter[player->GetGuildId()]++;

        challengeData->member.insert(member);
        if (sChallengeMgr->CheckBestMemberMapId(member.guid, challengeData))
            SendChallengeModeNewPlayerRecord(player);

        SendChallengeModeMapStatsUpdate(player);

        player->UpdateAchievementCriteria(CRITERIA_TYPE_INSTANSE_MAP_ID, _mapID, _rewardLevel);

        player->RemoveAura(ChallengersBurden);
        player->CastSpell(player, SPELL_CHALLENGE_ANTIKICK, true);
        player->KilledMonsterCredit(542180); // for daily event quest
    });

    if (GetChallengeTimer() < 9 * MINUTE)
    {
        sLog->outWarden("Detect cheating challenge timer less 540 seconds, challengeMap %u", _mapID);
        std::string cheaters = "";
        for (auto const& record : anticheatData)
            cheaters += record.second + " (" + std::to_string(record.first) + ") ";
        sLog->outWarden("Challenge cheaters: %s", cheaters.c_str());
    }

    for (auto const& v : guildCounter)
        if (v.second >= 3)
            challengeData->GuildID = v.first;

    sChallengeMgr->SetChallengeMapData(challengeData->ID, challengeData);
    sChallengeMgr->CheckBestMapId(challengeData);
    sChallengeMgr->CheckBestGuildMapId(challengeData);
    sChallengeMgr->SaveChallengeToDB(challengeData);
}

void Challenge::HitTimer()
{
    if (GetChallengeTimer() < _chestTimers[2])
        _rewardLevel = CHALLENGE_TIMER_LEVEL_3; /// 3 chests + 3 levels
    else if (GetChallengeTimer() < _chestTimers[1])
        _rewardLevel = CHALLENGE_TIMER_LEVEL_2; /// 2 chests + 2 levels
    else if (GetChallengeTimer() < _chestTimers[0])
        _rewardLevel = CHALLENGE_TIMER_LEVEL_1; /// 1 chest + 1 level
    else
        _rewardLevel = CHALLENGE_NOT_IN_TIMER;

    if (!_map)
        return;

    for (auto const& guid : _challengers)
        _countItems[guid] = 0;

    switch (_rewardLevel)
    {
        case CHALLENGE_TIMER_LEVEL_3: /// 3 chests + 3 levels
        case CHALLENGE_TIMER_LEVEL_2: /// 2 chests + 2 levels
        case CHALLENGE_TIMER_LEVEL_1: /// 1 chest + 1 level
        case CHALLENGE_NOT_IN_TIMER:  /// 0 chest
        {
            if (InstanceScript* script = GetInstanceScript())
            {
                if (GameObject* chest = _map->GetGameObject(script->_challengeChest))
                    chest->SetRespawnTime(7 * DAY);

                float _chance = sChallengeMgr->GetChanceItem(_rewardLevel, _challengeLevel);
                auto countItems = int32(_chance / 100.0f);
                _chance -= countItems * 100.0f;

                if (roll_chance_f(_chance))
                    countItems++;

                while (countItems > 0)
                {
                    auto _tempList = _challengers;
                    Trinity::Containers::RandomResizeList(_tempList, countItems);

                    for (auto const& guid : _tempList)
                    {
                        countItems--;
                        _countItems[guid] += 1;
                    }
                }
            }
        }
        default:
            break;
    }
}

uint32 Challenge::GetChallengeLevel() const
{
    return std::min(_challengeLevel, sWorld->getIntConfig(CONFIG_CHALLENGE_LEVEL_LIMIT));
}

std::array<uint32, 3> Challenge::GetAffixes() const
{
    return _affixes;
}

bool Challenge::HasAffix(Affixes affix)
{
    return _affixesTest.test(size_t(affix));
}

uint32 Challenge::GetChallengeTimerToNow() const
{
    return (getMSTime() - _challengeTimer) / IN_MILLISECONDS;
}

void Challenge::BroadcastPacket(WorldPacket const* data) const
{
    _map->ApplyOnEveryPlayer([&](Player* player)
    {
        player->SendDirectMessage(data);
    });
}

uint32 Challenge::GetChallengeTimer()
{
    if (!_challengeTimer)
        return 0;

    return _challengeTimer / IN_MILLISECONDS;
}

void Challenge::ModChallengeTimer(uint32 timer)
{
    if (!timer)
        return;

    _challengeTimer += timer * IN_MILLISECONDS;
}

void Challenge::ResetGo()
{
    if (InstanceScript* script = GetInstanceScript())
        for (ObjectGuid guid : script->_challengeDoorGuids)
            if (GameObject* challengeDoor = sObjectAccessor->FindGameObject(guid))
                challengeDoor->SetGoState(GO_STATE_READY);

    if (InstanceScript* script = GetInstanceScript())
        if (GameObject* challengeOrb = _map->GetGameObject(script->_challengeOrbGuid))
        {
            challengeOrb->SetGoState(GO_STATE_READY);
            challengeOrb->RemoveFlag(GAMEOBJECT_FIELD_FLAGS, GO_FLAG_NODESPAWN);
        }
}

void Challenge::SendStartTimer(Player* player)          //发送大秘境开始挑战倒计时
{
    WorldPackets::Instance::StartTimer startTimer;
    startTimer.Type = WORLD_TIMER_TYPE_CHALLENGE_MODE;
    startTimer.TimeRemaining = Seconds(ChallengeDelayTimer);
    startTimer.TotalTime = Seconds(ChallengeDelayTimer);
    if (player)
        player->SendDirectMessage(startTimer.Write());
    else
        BroadcastPacket(startTimer.Write());
}

void Challenge::SendStartElapsedTimer(Player* player)       
{
    WorldPackets::WorldState::StartElapsedTimer timer;      
    timer.Timer.TimerID = WORLD_TIMER_TYPE_CHALLENGE_MODE;
    timer.Timer.CurrentDuration = GetChallengeTimer();

    if (player)
        player->SendDirectMessage(timer.Write());
    else
        BroadcastPacket(timer.Write());
}

void Challenge::SendChallengeModeStart(Player* player)
{
    if (!_challengeEntry)
        return;

    WorldPackets::ChallengeMode::ChallengeModeStart start;
    start.MapID = _mapID;
    start.ChallengeID = _challengeEntry->ID;
    start.StartedChallengeLevel = _challengeLevel;
    start.Affixes = _affixes;

    if (player)
        player->SendDirectMessage(start.Write());
    else
        BroadcastPacket(start.Write());
}

void Challenge::SendChallengeModeNewPlayerRecord(Player* player)
{
    WorldPackets::ChallengeMode::ChallengeModeNewPlayerRecord newRecord;
    newRecord.MapID = _mapID;
    newRecord.CompletionMilliseconds = _challengeTimer;
    newRecord.StartedChallengeLevel = _rewardLevel;

    if (player)
        player->SendDirectMessage(newRecord.Write());
}

void Challenge::SendChallengeModeMapStatsUpdate(Player* player)
{
    if (!_challengeEntry)
        return;

    ChallengeByMap* bestMap = sChallengeMgr->BestForMember(player->GetGUID());
    if (!bestMap)
        return;

    auto itr = bestMap->find(_mapID);
    if (itr == bestMap->end())
        return;

    ChallengeData* best = itr->second;
    if (!best)
        return;

    WorldPackets::ChallengeMode::ChallengeModeMapStatsUpdate update;
    update.Stats.MapId = _mapID;
    update.Stats.BestCompletionMilliseconds = best->RecordTime;
    update.Stats.LastCompletionMilliseconds = _challengeTimer;
    update.Stats.ChallengeID = _challengeEntry->ID;
    update.Stats.BestMedalDate = best->Date;
    update.Stats.Affixes = best->Affixes;

    ChallengeMemberList members = best->member;
    for (auto const& v : members)
        update.Stats.BestSpecID.push_back(v.specId);

    if (player)
        player->SendDirectMessage(update.Write());
}

void Challenge::SummonWall(Player* /*player*/)
{
    if (InstanceScript* script = GetInstanceScript())
        for (ObjectGuid guid : script->_challengeDoorGuids)
            if (GameObject* challengeDoor = sObjectAccessor->FindGameObject(guid))
                challengeDoor->SetRespawnTime(7 * DAY);
}

void Challenge::SetInstanceScript(InstanceScript* instanceScript)
{
    _instanceScript = instanceScript;
}

InstanceScript* Challenge::GetInstanceScript() const
{
    return _instanceScript;
}

uint8 Challenge::GetItemCount(ObjectGuid guid) const
{
    auto itr = _countItems.find(guid);
    if (itr == _countItems.end())
        return 0;

    return itr->second;
}

uint8 Challenge::GetLevelBonus() const
{
    switch (_rewardLevel)
    {
        case CHALLENGE_TIMER_LEVEL_3: /// 3 chests + 3 levels
            return 2;
        case CHALLENGE_TIMER_LEVEL_2: /// 2 chests + 2 levels
            return 1;
        case CHALLENGE_TIMER_LEVEL_1: /// 1 chest + 1 level
        case CHALLENGE_NOT_IN_TIMER:  /// 0 chest
        default:
            return 0;
    }
}
