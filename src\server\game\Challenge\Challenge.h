/*
 * Copyright (C) 2008-2014 TrinityCore <http://www.trinitycore.org/>
 *
 * This program is free software; you can redistribute it and/or modify it
 * under the terms of the GNU General Public License as published by the
 * Free Software Foundation; either version 2 of the License, or (at your
 * option) any later version.
 *
 * This program is distributed in the hope that it will be useful, but WITHOUT
 * ANY WARRANTY; without even the implied warranty of MERCHANTABILITY or
 * FITNESS FOR A PARTICULAR PURPOSE. See the GNU General Public License for
 * more details.
 *
 * You should have received a copy of the GNU General Public License along
 * with this program. If not, see <http://www.gnu.org/licenses/>.
 */

#ifndef TRINITY_CHALLENGE_H
#define TRINITY_CHALLENGE_H

#include "Common.h"
#include "Scenario.h"
#include "InstanceScript.h"

struct ChallengeEntry;
class Scenario;

// 大秘境挑战法术枚举 - 定义挑战模式中使用的各种法术ID
enum ChallengeSpells : uint32
{
    ChallengersMight                = 206150, /// generic creature aura - 挑战者之力：通用生物光环，提升怪物属性
    ChallengersBurden               = 206151, /// generic player aura - 挑战者重负：通用玩家光环，增加挑战难度
    ChallengerBolstering            = 209859, // 激励词缀法术：怪物死亡时强化附近盟友
    ChallengerNecrotic              = 209858, // 死疽词缀法术：攻击时叠加治疗吸收效果
    ChallengerOverflowing           = 221772, // 溢出词缀法术：过量治疗转化为吸收护盾
    ChallengerSanguine              = 226489, // 血池词缀法术：怪物死亡后留下治疗血池
    ChallengerRaging                = 228318, // 暴怒词缀法术：低血量时怪物伤害大幅提升
    ChallengerSummonVolcanicPlume   = 209861, // 火山词缀召唤法术：召唤火山地刺
    ChallengerVolcanicPlume         = 209862, // 火山地刺法术：对玩家造成火焰伤害
    ChallengerBursting              = 240443, // 崩裂词缀法术：怪物死亡时爆炸伤害玩家
    ChallengerQuake                 = 240447, // 震荡词缀法术：玩家周期性产生冲击波
    ChallengerGrievousWound         = 240559, // 重伤词缀法术：低血量时持续伤害玩家

    //Explosive - 易爆词缀相关法术
    SPELL_FEL_EXPLOSIVES_SUMMON_1   = 240444, //Short dist - 邪能炸弹召唤法术（近距离）
    SPELL_FEL_EXPLOSIVES_SUMMON_2   = 243110, //Long dist - 邪能炸弹召唤法术（远距离）
    SPELL_FEL_EXPLOSIVES_VISUAL     = 240445, // 邪能炸弹视觉效果法术
    SPELL_FEL_EXPLOSIVES_DMG        = 240446, // 邪能炸弹爆炸伤害法术

    SPELL_CHALLENGE_ANTIKICK        = 305284, // 挑战模式防踢出法术：防止玩家被异常踢出副本
};

// 大秘境挑战NPC枚举 - 定义挑战模式中特殊NPC的ID
enum ChallengeNpcs : uint32
{
    NpcVolcanicPlume        = 105877, // 火山地刺NPC：火山词缀生成的地刺实体
    NPC_FEL_EXPLOSIVES      = 120651, // 邪能炸弹NPC：易爆词缀生成的爆炸物实体
};

// 大秘境挑战杂项数据枚举 - 定义挑战模式中使用的各种常量数据
enum MiscChallengeData : uint32
{
    ChallengeDelayTimer     = 10, // 挑战延迟计时器：挑战开始前的准备时间（秒）

};

class Challenge : public InstanceScript
{
public:
    Challenge(Map* map, Player* player, uint32 instanceID, Scenario* scenario);
    ~Challenge();

    void OnPlayerEnterForScript(Player* player) override;	// 当玩家进入副本时调用的脚本函数
    void OnPlayerLeaveForScript(Player* player) override;	// 当玩家离开副本时调用的脚本函数
    void OnPlayerDiesForScript(Player* player) override;	// 当玩家死亡时调用的脚本函数
    void OnCreatureCreateForScript(Creature* creature) override;	// 当副本内的生物被创建时调用的脚本函数
    void OnCreatureRemoveForScript(Creature* creature) override;	// 当副本内的生物被移除时调用的脚本函数
    void OnCreatureUpdateDifficulty(Creature* creature) override;	// 当副本内的生物难度更新时调用的脚本函数
    void EnterCombatForScript(Creature* creature, Unit* enemy) override;	// 当生物进入战斗状态时调用的脚本函数
    void CreatureDiesForScript(Creature* creature, Unit* killer) override;	// 当生物死亡时调用的脚本函数
    void OnGameObjectCreateForScript(GameObject* /*go*/) override {}		// 当游戏对象被创建时调用的脚本函数（此函数为空）
    void OnGameObjectRemoveForScript(GameObject* /*go*/) override {}		// 当游戏对象被移除时调用的脚本函数（此函数为空）
    void OnUnitCharmed(Unit* unit, Unit* charmer) override;			// 当单位被魅惑状态控制时调用的脚本函数
    void OnUnitRemoveCharmed(Unit* unit, Unit* charmer) override;	// 当单位的魅惑状态被移除时调用的脚本函数

    void Update(uint32 diff) override;			// 更新副本逻辑的函数，每次Tick都会调用

    bool CanStart();							// 判断是否可以开始副本
    void Start();								// 开始副本
    void Complete();							// 完成副本

    void BroadcastPacket(WorldPacket const* data) const override;			// 广播数据包到副本内的所有玩家

    void HitTimer();							// 计时器的处理逻辑 暂停计时器刷新箱子奖励

    uint32 GetChallengeLevel() const;			// 获取挑战模式的等级
    std::array<uint32, 3> GetAffixes() const;	// 获取挑战模式的词缀
    bool HasAffix(Affixes affix);				// 判断副本是否具有指定的词缀

    uint32 GetChallengeTimerToNow() const;		// 获取当前距离挑战模式开始的时间
    void ModChallengeTimer(uint32 timer);		// 修改挑战模式的计时器
    uint32 GetChallengeTimer();					// 获取挑战模式的计时器

    void ResetGo();								// 重置游戏对象（GameObject）的状态
    void SendStartTimer(Player* player = nullptr);			// 发送副本开始计时器给指定玩家（或所有玩家）
    void SendStartElapsedTimer(Player* player = nullptr);	// 发送副本已经经过的时间给指定玩家（或所有玩家）
    void SendChallengeModeStart(Player* player = nullptr);	// 发送挑战模式开始的消息给指定玩家（或所有玩家）
    void SendChallengeModeNewPlayerRecord(Player* player);	// 发送挑战模式新的玩家记录给指定玩家
    void SendChallengeModeMapStatsUpdate(Player* player);	// 发送挑战模式地图统计信息更新给指定玩家
    void SummonWall(Player* player);						// 召唤墙壁（特定功能的游戏对象）
    uint8 GetItemCount(ObjectGuid guid) const;				// 获取特定对象（ObjectGuid）的物品数量
    uint8 GetLevelBonus() const;							// 获取等级加成

    void SetInstanceScript(InstanceScript* instanceScript);	// 设置副本脚本（InstanceScript）
    InstanceScript* GetInstanceScript() const;				// 获取副本脚本（InstanceScript）

    GuidUnorderedSet _challengers;      // 挑战者GUID集合：存储参与当前挑战的所有玩家GUID
    bool _checkStart;                   // 检查开始标志：标记是否需要检查挑战开始条件
    bool _canRun;                       // 可运行标志：标记挑战是否满足运行条件
    bool _run;                          // 运行状态标志：标记挑战是否正在进行中
    bool _complete;                     // 完成状态标志：标记挑战是否已经完成

    ObjectGuid m_gguid;                 // 游戏对象GUID：关联的特殊游戏对象标识符
    ObjectGuid m_ownerGuid;             // 拥有者GUID：挑战发起者或钥石拥有者的GUID
    ObjectGuid m_itemGuid;              // 物品GUID：大秘境钥石物品的GUID

    uint32 _challengeTimer;             // 挑战计时器：记录挑战进行的总时间（毫秒）
    uint32 _affixQuakingTimer;          // 震荡词缀计时器：震荡词缀触发的间隔计时器

    FunctionProcessor m_Functions;      // 函数处理器：处理延迟执行的函数调用
    uint32 _mapID;                      // 地图ID：当前挑战所在的地图标识符

private:
    std::map<ObjectGuid, uint8> _countItems;                       // 物品计数映射表：记录特定对象的物品数量统计

    ObjectGuid _creator;                                            // 创建者GUID：挑战创建者的唯一标识符
    std::array<uint32, 3> _affixes;                                 // 词缀数组：存储当前挑战的3个词缀ID（4级、7级、10级词缀）
    std::bitset<size_t(Affixes::MaxAffixes)> _affixesTest;         // 词缀测试位集：用于快速检查特定词缀是否激活的位掩码
    uint16 _chestTimers[3];                                         // 宝箱计时器数组：存储3个等级宝箱的时间要求（铜、银、金）
    Item* _item;                                                    // 钥石物品指针：指向触发挑战的大秘境钥石物品
    Map* _map;                                                      // 地图指针：指向当前挑战所在的地图实例
    InstanceScript* _instanceScript;                                // 副本脚本指针：指向关联的副本脚本对象
    MapChallengeModeEntry const* _challengeEntry;                   // 挑战模式条目：指向地图挑战模式的数据库条目
    uint32 _challengeLevel;                                         // 挑战等级：当前大秘境的等级（钥石等级）
    uint32 _instanceID;                                             // 实例ID：当前副本实例的唯一标识符
    uint8 _rewardLevel;                                             // 奖励等级：完成挑战后的奖励等级（影响装备品质）
    bool _isKeyDepleted;                                            // 钥石耗尽标志：标记钥石是否因失败而降级
    Scenario* _scenario;                                            // 场景指针：指向关联的场景对象
    uint32 _deathCount = 0;                                         // 死亡计数：记录挑战过程中玩家的总死亡次数
};

#endif
